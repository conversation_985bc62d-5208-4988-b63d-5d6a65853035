<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    tools:context=".ui.profile.ProfileFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Profile Header Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp"
            app:cardBackgroundColor="?attr/colorPrimary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp">

                <!-- Profile Image -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    app:cardCornerRadius="60dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@color/white">

                    <ImageView
                        android:id="@+id/profile_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:contentDescription="@string/profile__image"
                        android:src="@drawable/ic_person"
                        android:padding="24dp"
                        android:scaleType="centerInside"
                        android:tint="?attr/colorPrimary" />

                </com.google.android.material.card.MaterialCardView>

                <!-- Profile Name -->
                <TextView
                    android:id="@+id/profile_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:gravity="center"
                    tools:text="John Doe" />

                <!-- Profile Position -->
                <TextView
                    android:id="@+id/profile_position"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textColor="@color/white"
                    android:alpha="0.9"
                    android:gravity="center"
                    tools:text="Developer at ACME Inc" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Change Password Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_change_password"
            style="@style/Widget.MaterialComponents.Button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/profile__change_password"
            android:layout_marginBottom="16dp"
            android:textColor="@color/change_password_button_fg"
            android:backgroundTint="@color/change_password_button_bg"
            app:cornerRadius="16dp"
            app:icon="@drawable/ic_lock"
            app:iconGravity="textStart" />

        <!-- Account Information Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_account_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="اطلاعات حساب کاربری"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Username -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__username"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_username"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        android:textStyle="bold"
                        tools:text="user1" />
                </LinearLayout>

                <!-- Email -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__email"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="<EMAIL>" />
                </LinearLayout>

                <!-- Short UUID -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__short_uuid"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_short_uuid"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        android:fontFamily="monospace"
                        tools:text="da1e120e" />
                </LinearLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Personal Information Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_personal_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="اطلاعات شخصی"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- First Name -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__first_name"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_first_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="John" />
                </LinearLayout>

                <!-- Last Name -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__last_name"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_last_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="Doe" />
                </LinearLayout>

                <!-- Gender -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__gender"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_gender"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="Male" />
                </LinearLayout>

                <!-- Description -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__description"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_description"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="Software developer with 5 years of experience" />
                </LinearLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Work Information Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_work_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="اطلاعات شغلی"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Company -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__company"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_company"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="ACME Inc" />
                </LinearLayout>

                <!-- Position -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__position"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_position_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="Developer" />
                </LinearLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Permissions Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_permissions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="سطح دسترسی"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Is Superuser -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__is_superuser"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_is_superuser"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="No" />
                </LinearLayout>

                <!-- Is Limited Admin -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/profile__is_limited_admin"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/profile_is_limited_admin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="Yes" />
                </LinearLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Loading and Error States -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/error_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
            android:textColor="@color/error"
            android:visibility="gone"
            tools:text="Error loading profile" />

    </LinearLayout>
</androidx.core.widget.NestedScrollView>
