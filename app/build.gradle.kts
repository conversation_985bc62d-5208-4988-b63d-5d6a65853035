import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.navigation.safeargs)
    id("kotlin-parcelize")
}

android {
    namespace = "ir.rahavardit.ariel"
    compileSdk = 35

    defaultConfig {
        applicationId = "ir.rahavardit.ariel"  // نام برنامه در مارکت
        minSdk = 33
        targetSdk = 35
        versionCode = 1        // کد نسخه (عدد صحیح - برای مارکت مهمه)
        versionName = "1.0.5"  // نام نسخه (به کاربر نمایش داده میشه)

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    // __BY_ME__
    // conditionally set url
    flavorDimensions += "default"
    productFlavors {
        create("emulator") {
            dimension = "default"
            manifestPlaceholders["BACKUP_IS_ALLOWED"] = "true"
            buildConfigField("String", "BASE_URL", "\"http://10.0.2.2:8000\"")
        }
        create("phone") {
            dimension = "default"
            manifestPlaceholders["BACKUP_IS_ALLOWED"] = "true"
            buildConfigField("String", "BASE_URL", "\"http://192.168.1.100:8000\"")
        }
        create("production") {
            dimension = "default"

            // disable backup only for production
            // (will be used in AndroidManifest.xml)
            manifestPlaceholders["BACKUP_IS_ALLOWED"] = "false"


            // STEPs for BASE_URL:

            // STEP 1/4: load .env and validate
            val envFile = rootProject.file(".env")
            val envProps = Properties().apply {
                if (envFile.exists()) {
                    envFile.inputStream().use { load(it) }
                }
            }

            // STEP 2/4: get PRODUCTION_BASE_URL
            val productionBaseUrl = envProps.getProperty("PRODUCTION_BASE_URL")

            // STEP 3/4: validate PRODUCTION_BASE_URL
            if (productionBaseUrl.isNullOrBlank()) {
                throw GradleException(
                    "Missing or empty PRODUCTION_BASE_URL in .env file.\n" +
                    "Please create/update the .env file in the project root with a line like:\n" +
                    "PRODUCTION_BASE_URL=https://your.example.ir"
                )
            }

            // STEP 4/4: set BASE_URL
            buildConfigField("String", "BASE_URL", "\"$productionBaseUrl\"")
        }
    }

    buildTypes {

        // __BY_ME__ release -> getByName("release")
        getByName("release") {

            // __BY_ME__
            isDebuggable = false

            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit.gson)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging)
    implementation(libs.gson)

    // Coroutines
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}
